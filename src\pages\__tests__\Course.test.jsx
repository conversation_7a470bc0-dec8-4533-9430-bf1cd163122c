import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { vi } from 'vitest';
import Course from '../Course';
import { useAuthStore } from '../../stores/authStore';
import { coursesAPI } from '../../services/api';

// Mock the auth store
vi.mock('../../stores/authStore');
vi.mock('../../services/api');

// Mock SideNavBar component
vi.mock('../../components/layout/SideNavBar', () => ({
  default: ({ activeTab, onTabChange, onLogout, currentUser }) => (
    <div data-testid="side-navbar">
      <span>Active Tab: {activeTab}</span>
      <button onClick={() => onTabChange('home')}>Home</button>
      <button onClick={() => onTabChange('courses')}>Courses</button>
      <button onClick={onLogout}>Logout</button>
      <span>User: {currentUser?.email}</span>
    </div>
  )
}));

// Mock DataTable component
vi.mock('../../components/ui/DataTable', () => ({
  default: ({ data, loading, error, columns, title }) => (
    <div data-testid="data-table">
      {loading && <div>Loading courses...</div>}
      {error && <div>Error: {error}</div>}
      {!loading && !error && (
        <table>
          <thead>
            <tr>
              {columns.map((col, index) => (
                <th key={index}>{col.header}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((course, index) => (
              <tr key={index}>
                <td>{course.courseName}</td>
                <td>{course.price}</td>
                <td>{course.duration}</td>
                <td>Edit</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  )
}));

const mockCourseData = {
  data: [
    {
      id: 1,
      title: 'Structural Design - 1',
      originalPrice: 5000,
      offerPrice: 3000,
      status: 'Active',
      description: 'Test course description',
      tags: ['engineering', 'structural'],
      displayPicture: 'test-image.jpg',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01'
    },
    {
      id: 2,
      title: 'Structural Design - 2',
      originalPrice: 6000,
      offerPrice: null,
      status: 'Active',
      description: 'Another test course',
      tags: ['engineering'],
      displayPicture: 'test-image2.jpg',
      createdAt: '2024-01-02',
      updatedAt: '2024-01-02'
    }
  ]
};

describe('Course Component', () => {
  const mockCurrentUser = {
    id: 'test-user',
    email: '<EMAIL>'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    useAuthStore.mockReturnValue({
      currentUser: mockCurrentUser,
      logout: vi.fn()
    });
  });

  it('should render course page with header and table', async () => {
    coursesAPI.getAll.mockResolvedValue(mockCourseData);

    render(
      <BrowserRouter>
        <Course />
      </BrowserRouter>
    );

    // Check if main elements are rendered
    expect(screen.getByText('Courses')).toBeInTheDocument();
    expect(screen.getByText('List of courses')).toBeInTheDocument();
    expect(screen.getByText('Add A New Course')).toBeInTheDocument();
    expect(screen.getByTestId('side-navbar')).toBeInTheDocument();
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
  });

  it('should fetch and display courses correctly', async () => {
    coursesAPI.getAll.mockResolvedValue(mockCourseData);

    render(
      <BrowserRouter>
        <Course />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(coursesAPI.getAll).toHaveBeenCalledWith({
        offset: 0,
        limit: 50
      });
    });

    // Check if course data is displayed
    await waitFor(() => {
      expect(screen.getByText('Structural Design - 1')).toBeInTheDocument();
      expect(screen.getByText('Structural Design - 2')).toBeInTheDocument();
    });
  });

  it('should handle API errors gracefully', async () => {
    const errorMessage = 'Failed to fetch courses';
    coursesAPI.getAll.mockRejectedValue(new Error(errorMessage));

    render(
      <BrowserRouter>
        <Course />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(`Error: ${errorMessage}`)).toBeInTheDocument();
    });
  });

  it('should show loading state initially', () => {
    coursesAPI.getAll.mockImplementation(() => new Promise(() => {})); // Never resolves

    render(
      <BrowserRouter>
        <Course />
      </BrowserRouter>
    );

    expect(screen.getByText('Loading courses...')).toBeInTheDocument();
  });

  it('should format prices correctly', async () => {
    coursesAPI.getAll.mockResolvedValue(mockCourseData);

    render(
      <BrowserRouter>
        <Course />
      </BrowserRouter>
    );

    await waitFor(() => {
      // First course should show offer price
      expect(screen.getByText('₹3000')).toBeInTheDocument();
      // Second course should show original price (no offer price)
      expect(screen.getByText('₹6000')).toBeInTheDocument();
    });
  });
});
