/* DataTable Styles - Admin Portal Design */
:root {
  /* Admin Portal Table Colors - Cream Theme */
  --table-bg: #FFF8EB;
  --table-header-bg: #FFF8EB;
  --table-row-bg: #FFF8EB;
  --table-hover-bg: #f9f9f9;
  --table-border: #e0e0e0;
  --table-text: #374151;
  --table-header-text: #374151;
  --table-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  --border-radius: 12px;
}

/* Base Table Container */
.data-table-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0px 0 24px; /* No horizontal padding - handled by dashboard-main-content */
  box-sizing: border-box;
  gap: 0px; /* Tight spacing between header and table */
}

.data-table-container {
  background-color: var(--table-bg);
  border-radius: var(--border-radius);
  border: 1px solid var(--table-border);
  box-shadow: var(--table-shadow);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0; /* No padding for flush design */
  margin: 0;
}

.data-table-wrapper {
  overflow-x: auto;
  width: 100%;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  border: none; /* Remove wrapper border */
  margin: 0;
  border-radius: 0;
}

/* Table Content */
.data-table-content {
  width: 100%;
  margin: 0;
  padding: 0;
}

.data-table-page h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--table-text);
  margin: 0 0 24px 0;
  padding: 0;
}

/* Table Structure */
.data-table {
  width: 100%;
  border-collapse: collapse; /* Changed from separate to collapse */
  border-spacing: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--table-text);
  background-color: var(--table-bg);
  margin: 0;
  padding: 0;
  table-layout: auto;
  border: none; /* Remove table border */
}

/* Table Header */
.data-table thead {
  background-color: var(--table-header-bg);
  position: sticky;
  top: 0;
  z-index: 1;
  border-bottom: 1px solid var(--table-border);
}

.data-table th {
  padding: 16px 20px; /* Increased padding to match courses table */
  text-align: left;
  font-weight: 600; /* Increased font weight */
  font-size: 14px; /* Increased font size */
  text-transform: none; /* Remove uppercase */
  letter-spacing: 0; /* Remove letter spacing */
  color: var(--table-header-text);
  background-color: var(--table-header-bg);
  border-bottom: 1px solid var(--table-border);
  border-right: none; /* Remove right border */
  white-space: nowrap;
  position: relative;
}

.data-table th:last-child {
  text-align: center; /* Center align last column (typically actions) */
}

/* Table Body */
.data-table tbody tr {
  background-color: var(--table-row-bg);
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0; /* Lighter border for rows */
}

.data-table tbody tr:last-child {
  border-bottom: none;
}

.data-table tbody tr:hover {
  background-color: var(--table-hover-bg);
}

.data-table td {
  padding: 16px 20px; /* Match header padding */
  vertical-align: middle;
  border-right: none; /* Remove right border */
  border-bottom: none; /* Remove bottom border - handled by tr */
  font-size: 14px;
  line-height: 1.5;
  color: var(--table-text);
}

/* Interactive Rows */
.data-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 0.3px;
}

.status-active {
  background-color: #E8F5E8;
  color: #2D5A2D;
}

.status-inactive {
  background-color: #FFF2E8;
  color: #8B4513;
}

.status-draft {
  background-color: #F0F0F0;
  color: #666666;
}

.status-unknown {
  background-color: #F5F5F5;
  color: #666666;
}



/* Action Buttons */
.course-edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.course-edit-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* Generic Action Buttons */
.action-button {
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #ffffff;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.action-button:hover {
  background-color: #374151;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.data-table-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Loading, Error, and Empty States */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  background-color: #FFF8EB;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.loading-spinner {
  width: 36px;
  height: 36px;
  border: 3px solid #e0e0e0;
  border-top: 3px solid #374151;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message,
.error-message,
.empty-message {
  color: #374151;
  font-size: 14px;
  margin: 0;
}

.retry-button {
  margin-top: 16px;
  padding: 10px 24px;
  background-color: #374151;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.retry-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.error-message {
  color: #D32F2F;
}

/* Responsive Design - Admin Portal Breakpoints */
@media (max-width: 1024px) {
  .data-table th,
  .data-table td {
    padding: 14px 16px;
  }
}

@media (max-width: 768px) {
  .data-table-wrapper {
    border-radius: 8px;
    overflow-x: auto;
  }

  .data-table {
    min-width: 100%;
  }

  .data-table th,
  .data-table td {
    padding: 12px 14px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .data-table th,
  .data-table td {
    padding: 12px 16px;
    font-size: 13px;
  }

  .action-button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .status-badge {
    padding: 3px 10px;
    font-size: 12px;
  }

  .course-edit-btn {
    padding: 6px;
  }

  .course-edit-btn svg {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .data-table th,
  .data-table td {
    padding: 10px 12px;
    font-size: 13px;
  }

  .action-button {
    padding: 4px 8px;
    font-size: 12px;
  }

  .status-badge {
    padding: 2px 6px;
    font-size: 11px;
  }
}
