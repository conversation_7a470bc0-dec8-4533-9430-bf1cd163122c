/* DataTable Styles */
:root {
  /* Table Colors */
  --table-bg: #FFF8EB;
  --table-header-bg: #FFF8EB;
  --table-row-bg: #FFFFFF;
  --table-hover-bg: #FFF0D9;
  --table-border: #E5E7EB;
  --table-text: #1F2937;
  --table-header-text: #4B5563;
  --table-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  --border-radius: 8px;
}

/* Base Table Container */
.data-table-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px 40px;
  box-sizing: border-box;
}

.data-table-container {
  background-color: var(--table-bg);
  border-radius: var(--border-radius);
  border: 1px solid var(--table-border);
  box-shadow: var(--table-shadow);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.data-table-wrapper {
  overflow-x: auto;
  width: 100%;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
}

/* Table Content */
.data-table-content {
  width: 100%;
  margin: 0;
  padding: 0;
}

.data-table-page h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--table-text);
  margin: 0 0 24px 0;
  padding: 0;
}

/* Table Structure */
.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--table-text);
  background-color: var(--table-bg);
  margin: 0;
  padding: 0;
  table-layout: auto;
}

/* Table Header */
.data-table thead {
  background-color: var(--table-header-bg);
  position: sticky;
  top: 0;
  z-index: 1;
}

.data-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 500;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--table-header-text);
  background-color: var(--table-header-bg);
  border-bottom: 2px solid var(--table-border);
  white-space: nowrap;
  position: relative;
}

.data-table th:first-child {
  padding-left: 24px;
}

.data-table th:last-child {
  padding-right: 24px;
}

/* Table Body */
.data-table tbody tr {
  background-color: var(--table-row-bg);
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--table-border);
}

.data-table tbody tr:last-child {
  border-bottom: none;
}

.data-table tbody tr:hover {
  background-color: var(--table-hover-bg);
}

.data-table td {
  padding: 16px;
  vertical-align: middle;
  border-bottom: 1px solid var(--table-border);
}

.data-table td:first-child {
  padding-left: 24px;
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

.data-table td:last-child {
  padding-right: 24px;
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
}

/* Last row styling */
.data-table tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--border-radius);
}

.data-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--border-radius);
}

.data-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: 8px;
}

.data-table td {
  padding: 12px 16px;
  font-size: 14px;
  color: var(--table-text);
  border-right: 1px solid var(--table-border);
  border-bottom: 1px solid var(--table-border);
  vertical-align: middle;
  background-color: var(--table-row-bg);
  transition: background-color 0.2s ease;
}

.data-table tbody tr:hover td {
  background-color: rgba(255, 255, 255, 0.5);
}

.data-table td:last-child {
  border-right: none;
}

/* Interactive Rows */
.data-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.data-row:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: var(--caption-size);
  font-weight: var(--font-weight-medium);
  text-transform: capitalize;
  letter-spacing: 0.3px;
}

.status-active {
  background-color: #E8F5E8;
  color: #2D5A2D;
}

.status-inactive {
  background-color: #FFF2E8;
  color: #8B4513;
}

.status-draft {
  background-color: #F0F0F0;
  color: #666666;
}

.status-unknown {
  background-color: #F5F5F5;
  color: var(--secondary-text-color);
}

/* Action Buttons */
.action-button {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: var(--caption-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.action-button:hover {
  background-color: var(--text-color);
  color: var(--bg-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.action-button .button-icon {
  font-size: 16px;
  line-height: 1;
}

/* Table Footer */
.data-table-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Loading, Error, and Empty States */
.table-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  background-color: var(--bg-color);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.loading-spinner {
  width: 36px;
  height: 36px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--text-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  margin-top: 16px;
  padding: 10px 24px;
  background-color: var(--text-color);
  color: var(--bg-color);
  border: none;
  border-radius: 6px;
  font-size: var(--body3-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.retry-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.text-error {
  color: #D32F2F;
  margin-top: 8px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .data-table-container {
    padding: 20px;
  }

  .data-table th,
  .data-table td {
    padding: 14px 16px;
  }
}

@media (max-width: 768px) {
  .data-table-container {
    padding: 16px;
  }

  .data-table-wrapper {
    border-radius: 8px;
    overflow-x: auto;
  }

  .data-table {
    min-width: 100%;
  }

  .data-table th,
  .data-table td {
    padding: 12px 14px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .data-table-container {
    padding: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .action-button {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .status-badge {
    padding: 3px 10px;
    font-size: 12px;
  }
}

/* Data Table Page Layout */
.data-table-page {
  display: flex;
  flex-direction: column;
  gap: 0px; /* Removed gap for tighter spacing between header and table */
  padding: 0px 0 24px; /* Remove horizontal padding - handled by dashboard-main-content */
  max-width: 1400px;
  margin: 0 auto;
}

/* .data-table-header {
  padding: 2px 0 12px;
  margin: 0;
}

.data-table-header .header-content {
  max-width: 1200px;
  margin: 0 auto;
} */

.data-table-content {
  width: 100%;
}

.data-table-container {
 
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 24px;
}

.data-table-wrapper {
  overflow-x: auto;
  border-radius: 8px;

  margin-top: 16px;
  border: 1px solid #ddd;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-family-default);
  border: 1px solid #ddd;
}

.data-table thead {
  
  border-bottom: 2px solid #ddd;
}

.data-table th {
  padding: 16px 12px;
  text-align: left;
  font-weight: var(--font-weight-semibold);
  font-size: var(--body3-size);
  color: var(--text-color);
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  white-space: nowrap;
  background-color: var(--bg-color);
}

.data-table th:last-child {
  border-right: none;
}

.data-table tbody tr {
  border-bottom: 1px solid #ddd;
  background-color: var(--bg-color);
}



.data-table tbody tr:last-child {
  border-bottom: 1px solid #ddd;
}

.data-table td {
  padding: 16px 12px;
  font-size: var(--body3-size);
  color: var(--text-color);
  border-right: 1px solid #ddd;
  vertical-align: top;
}

.data-table td:last-child {
  border-right: none;
}

/* Generic column styles - can be overridden with custom classes */
.data-row {
  cursor: pointer;
}

/* Status badge styles - commonly used for status columns */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: var(--caption-size);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background-color: #E8F5E8;
  color: #2D5A2D;
}

.status-inactive {
  background-color: #FFF2E8;
  color: #8B4513;
}

.status-draft {
  background-color: #F0F0F0;
  color: #666666;
}

.status-unknown {
  background-color: #F5F5F5;
  color: var(--secondary-text-color);
}

/* Action button styles */
.data-actions {
  min-width: 100px;
  text-align: center;
}

.action-button {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: var(--caption-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: var(--text-color);
  color: var(--bg-color);
  transform: translateY(-1px);
}

.view-button {
  border-color: #4A90E2;
  color: #4A90E2;
}

.view-button:hover {
  background-color: #4A90E2;
  color: white;
}

.data-table-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  text-align: center;
}

/* Loading, Error, and Empty States */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--text-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: var(--text-color);
  color: var(--bg-color);
  border: none;
  border-radius: 4px;
  font-size: var(--body3-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.retry-button:hover {
  opacity: 0.8;
}

.text-error {
  color: #D32F2F;
}

/* Responsive Design - Matches AddCourse page breakpoints */
@media (max-width: 1024px) {
  .data-table-container {
    padding: 24px;
  }

  .data-table th,
  .data-table td {
    padding: 12px 8px;
  }

  .course-page-header {
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .data-table-container {
    padding: 16px;
  }

  .data-table-header {
    padding: 8px 0 12px;
  }

  .data-table-wrapper {
    font-size: var(--caption-size);
    border-radius: 8px;
    overflow-x: auto;
  }

  .data-table {
    min-width: 100%;
  }

  .data-table th,
  .data-table td {
    padding: 10px 8px;
  }

  .course-page-header {
    margin-bottom: 20px;
  }

  /* .course-page-header .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  } */

  .add-course-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .data-table-container {
    padding: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 6px;
    font-size: 13px;
  }

  .course-page-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }

  .data-table {
    font-size: 13px;
  }

  .action-button {
    padding: 4px 8px;
    font-size: 12px;
  }

  .status-badge {
    padding: 2px 6px;
    font-size: 11px;
  }
}
