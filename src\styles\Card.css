/* Card Component Styles */
.card {
  background: #FFFFFF; /* White background for cards */
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease, transform 0.2s ease;
  overflow: hidden;
  width: 100%;
  max-width: 320px;
  margin: 0 auto;
}

.card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 220px;
}

.card-image-container {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #FAFAFA;
  position: relative;
  height: 120px;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card:hover .card-image {
  transform: scale(1.05);
}

/* Placeholder for missing images */
.card-image-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  z-index: 1;
}

.card-image-placeholder::after {
  content: '🏗️';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 32px;
  z-index: 2;
  opacity: 0.6;
}

.card-title {
  margin: 0 0 12px 0;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 12px;
}

.card-duration-section {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--secondary-text-color);
}

.card-duration {
  color: var(--secondary-text-color);
}

/* Clock Icon Styles */
.card-clock-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--secondary-text-color);
  padding: 2px;
}

.card-clickable {
  cursor: pointer;
}

/* Example Container Styles */
.card-examples-container {
  padding: 40px;
  background-color: var(--bg-color);
  min-height: 100vh;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    max-width: 100%;
  }

  .card-content {
    padding: 16px;
  }

  .card-image {
    height: 100px;
  }

  .card-examples-container {
    padding: 20px;
  }
}
