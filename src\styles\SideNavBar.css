/* SideNavBar Component Styles */
.side-navbar {
  width: 240px;
  background-color: #FFF8EB; /* Warm cream sidebar background */
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  padding: 24px 0;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 100;
}

/* Logo Section */
.navbar-logo {
  display: flex;
  justify-content: center;
}

.navbar-logo img {
  /* padding: 0 24px 32px; */
  align-items: center;
  margin-bottom: 24px;
  /* width: 6rem; */
}

/* Navigation Items */
.navbar-nav {
  flex: 1;
  padding: 0 16px;
}

.nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 4px;
  text-align: left;
}

.nav-item:hover {
  background-color: #f3f4f6;
}

.nav-item.active {
  background-color: var(--border-color);
  color: var(--text-color);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-label {
  font-size: var(--body3-size);
  font-weight: var(--font-weight-regular);
}

/* Logout Section */
.navbar-logout {
  padding: 0 16px;
  border-top: 1px solid var(--border-color);
  padding-top: 24px;
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ef4444;
}

.logout-btn:hover {
  background-color: #fef2f2;
}

.logout-icon {
  width: 20px;
  height: 20px;
}

.logout-label {
  font-size: var(--body3-size);
  font-weight: var(--font-weight-regular);
}

/* Responsive Design */
@media (max-width: 768px) {
  .side-navbar {
    width: 200px;
  }
}

@media (max-width: 640px) {
  .side-navbar {
    width: 100%;
    position: fixed;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .side-navbar.open {
    transform: translateX(0);
  }
}
