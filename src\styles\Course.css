/* Course Page Specific Styles */

/* Page Layout */
.courses-page-container {
  background-color: #FFFBF3;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Styles */
.courses-header-card {
  background-color: #FFF8EB;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 12px 12px;
  box-shadow: none;
  padding: 24px 40px;
  text-align: center;
  margin: 0;
}

.courses-page-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
}

/* Controls Section */
.courses-controls {
  padding: 0 40px;
  margin: 32px 0;
  max-width: 1400px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.courses-controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 20px 0px;
}

.courses-breadcrumb {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
}

.courses-breadcrumb span {
  font-size: 16px;
  color: #6b7280;
  text-decoration: underline;
  font-weight: 400;
  cursor: pointer;
  transition: color 0.2s ease;
}

.courses-breadcrumb span:hover {
  color: #4b5563;
}

.courses-add-btn {
  background-color: #1f2937;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.courses-add-btn:hover {
  background-color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* DataTable Overrides */
.courses-datatable-wrapper {
  flex: 1;
  /* padding: 0 40px 40px; */
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

.courses-datatable .data-table-container {
  background-color: #FFF8EB;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 0;
  margin: 0;
  border-radius: 12px;
  overflow: hidden;
}

.courses-datatable .data-table-wrapper {
  border: none;
  margin: 0;
  border-radius: 0;
  overflow-x: auto;
}

.courses-datatable .data-table {
  border: none;
  font-size: 14px;
  background-color: #FFF8EB;
}

.courses-datatable .data-table thead {
  border-bottom: 1px solid #e0e0e0;
}

.courses-datatable .data-table th {
  background-color: #FFF8EB;
  color: #374151;
  font-weight: 600;
  font-size: 14px;
  border-right: none;
  border-bottom: 1px solid #e0e0e0;
  padding: 16px 20px;
  white-space: nowrap;
}

.courses-datatable .data-table th:last-child {
  text-align: center;
}

.courses-datatable .data-table tbody tr {
  background-color: #FFF8EB;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.courses-datatable .data-table tbody tr:last-child {
  border-bottom: none;
}

.courses-datatable .data-table tbody tr:hover {
  background-color: #f9f9f9;
}

.courses-datatable .data-table td {
  padding: 16px 20px;
  color: #374151;
  border-right: none;
  vertical-align: middle;
  font-size: 14px;
  line-height: 1.5;
}

/* Column-specific styles */
.course-name-cell {
  font-weight: 500;
  color: #1f2937;
}

.course-price-cell {
  font-weight: 500;
  color: #374151;
}

.course-duration-cell {
  color: #6b7280;
}

.course-edit-cell {
  text-align: center;
  vertical-align: middle;
}

.course-edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.course-edit-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.course-edit-btn svg {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.course-edit-btn:hover svg {
  transform: scale(1.1);
}

/* Loading/Error States */
.courses-datatable .table-state-container {
  background-color: #FFF8EB;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  margin: 0;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 40px 24px;
}

.courses-datatable .loading-spinner {
  border-top-color: #1f2937;
  margin-bottom: 16px;
}

.courses-datatable .retry-button {
  background-color: #1f2937;
  color: white;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 16px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.courses-datatable .retry-button:hover {
  background-color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .courses-datatable-wrapper,
  .courses-controls,
  .courses-header-card {
    padding-left: 32px;
    padding-right: 32px;
  }
}

@media (max-width: 1024px) {
  .courses-header-card {
    padding: 20px 24px;
  }

  .courses-controls,
  .courses-datatable-wrapper {
    padding-left: 24px;
    padding-right: 24px;
    margin: 24px 0;
  }

  .courses-page-title {
    font-size: 28px;
  }

  .courses-add-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .courses-header-card {
    padding: 16px 20px;
  }

  .courses-controls,
  .courses-datatable-wrapper {
    padding-left: 20px;
    padding-right: 20px;
    margin: 20px 0;
  }

  .courses-page-title {
    font-size: 24px;
  }

  .courses-controls-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .courses-breadcrumb {
    text-align: center;
  }

  .courses-add-btn {
    width: 100%;
    justify-content: center;
  }

  .courses-datatable .data-table {
    min-width: 600px;
  }
}

@media (max-width: 576px) {
  .courses-header-card {
    padding: 12px 16px;
  }

  .courses-controls,
  .courses-datatable-wrapper {
    padding-left: 16px;
    padding-right: 16px;
    margin: 16px 0;
  }

  .courses-page-title {
    font-size: 20px;
  }

  .courses-breadcrumb span {
    font-size: 14px;
  }

  .courses-add-btn {
    padding: 10px 16px;
    font-size: 12px;
  }

  .courses-datatable .data-table th,
  .courses-datatable .data-table td {
    padding: 12px 16px;
    font-size: 13px;
  }

  .course-edit-btn {
    padding: 6px;
  }

  .course-edit-btn svg {
    width: 14px;
    height: 14px;
  }
}