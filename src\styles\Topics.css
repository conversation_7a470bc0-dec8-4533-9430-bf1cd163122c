/* Topics Page Specific Styles */

/* Page Layout */
.topics-page-container {
  background-color: #FFFBF3;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding-bottom: 40px;
}

/* Header Styles */
.topics-header-card {
  background-color: #FFF8EB;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 12px 12px;
  padding: 24px 40px;
  margin: 0;
}

.topics-header h1 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
}

.topics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.topics-page-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.course-description {
  color: #666;
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

/* Controls Section - Match Course page structure */
.topics-controls {
  padding: 0 40px;
  margin: 32px 0;
  max-width: 1400px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.topics-controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 20px 0px;
}

.topics-breadcrumb {
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.topics-breadcrumb a {
  color: #3498db;
  text-decoration: none;
  transition: color 0.2s;
  font-weight: 500;
}

.breadcrumb a:hover {
  color: #2980b9;
  text-decoration: underline;
}

.breadcrumb-separator {
  color: #95a5a6;
  font-size: 0.8em;
}

.topics-breadcrumb span {
  font-size: 16px;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.2s ease;
}

.topics-add-btn {
  background-color: #1f2937;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.topics-add-btn:hover {
  background-color: #374151;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* DataTable Container - Match Course page styling */
.topics-datatable-wrapper {
  flex: 1;
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Remove redundant styles as they're now handled by the DataTable component */

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-active {
  background-color: #E8F5E8;
  color: #2D5A2D;
}

.status-draft {
  background-color: #FFF2E8;
  color: #8B4513;
}

.status-inactive {
  background-color: #F0F0F0;
  color: #666666;
}

/* Action Buttons */
.topic-edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.topic-edit-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.topic-edit-btn svg {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.topic-edit-btn:hover svg {
  transform: scale(1.1);
}

/* Responsive Design - Match Course page breakpoints */
@media (max-width: 1024px) {
  .topics-controls,
  .topics-header-card {
    padding-left: 32px;
    padding-right: 32px;
  }
}

@media (max-width: 768px) {
  .topics-header-card {
    padding: 20px 24px;
  }

  .topics-controls {
    padding-left: 24px;
    padding-right: 24px;
    margin: 24px 0;
  }

  .topics-page-title {
    font-size: 28px;
  }

  .topics-add-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .topics-header-card {
    padding: 12px 16px;
  }

  .topics-controls {
    padding-left: 16px;
    padding-right: 16px;
    margin: 16px 0;
  }

  .topics-page-title {
    font-size: 20px;
  }

  .topics-breadcrumb span {
    font-size: 14px;
  }

  .topics-add-btn {
    padding: 10px 16px;
    font-size: 12px;
  }
}
