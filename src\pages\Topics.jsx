import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { topicsAPI, coursesAPI } from '../services/api';
import SideNavBar from '../components/layout/SideNavBar';
import DataTable from '../components/ui/DataTable';
import { toast } from 'react-toastify';
import '../styles/Topics.css';

const Topics = () => {
  const navigate = useNavigate();
  const { courseId } = useParams();
  const { currentUser, logout } = useAuthStore();
  const [topics, setTopics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [course, setCourse] = useState(null);

  // Fetch course details and topics
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch course details from the list of all courses
        const coursesResponse = await coursesAPI.getAll();
        const currentCourse = coursesResponse.data?.find(course => course.id === courseId);

        if (!currentCourse) {
          throw new Error('Course not found');
        }

        setCourse({
          id: currentCourse.id,
          title: currentCourse.title,
          description: currentCourse.description || 'No description available'
        });

        // Fetch topics for the course
        const topicsResponse = await topicsAPI.getByCourseId(courseId);

        // Transform the API response to match our table structure
        const formattedTopics = topicsResponse.data.data.map(topic => ({
          id: topic.id,
          title: topic.title,
          order: topic.orderIndex || 0,
          status: topic.status?.toLowerCase() || 'draft',
          description: topic.description || ''
        }));

        setTopics(formattedTopics);

      } catch (error) {
        console.error('Error fetching data:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to load course data';
        setError(errorMessage);
        toast.error(`Error: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    if (courseId) {
      fetchData();
    } else {
      setError('No course ID provided');
      setLoading(false);
    }
  }, [courseId]);

  const handleTabChange = (tabId) => {
    if (tabId === 'home') {
      navigate('/dashboard');
    } else if (tabId === 'courses') {
      navigate('/course');
    } else if (tabId === 'profile') {
      navigate('/profile');
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleAddTopic = async () => {
    try {
      // Create a new topic with default values
      const newTopic = {
        courseId,
        title: 'New Topic',
        description: 'Enter topic description',
        order: topics.length + 1,
        status: 'draft'
      };

      const response = await topicsAPI.create(newTopic);

      if (response.data) {
        // Navigate to edit page for the new topic
        navigate(`/course/${courseId}/topics/${response.data.id}/edit`);
        toast.success('New topic created successfully');
      }
    } catch (error) {
      console.error('Error creating new topic:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to create new topic';
      toast.error(`Error: ${errorMessage}`);
    }
  };

  const handleEditTopic = (topic) => {
    // Navigate to edit page for the topic
    navigate(`/course/${courseId}/topics/${topic.id}/edit`);  };

  const handleDeleteTopic = async (topicId, e) => {
    e.stopPropagation();

    if (window.confirm('Are you sure you want to delete this topic? This action cannot be undone.')) {
      try {
        await topicsAPI.delete(topicId);

        // Remove the topic from the local state
        setTopics(topics.filter(topic => topic.id !== topicId));
        toast.success('Topic deleted successfully');
      } catch (error) {
        console.error('Error deleting topic:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to delete topic';
        toast.error(`Error: ${errorMessage}`);
      }
    }
  };

  const formatStatus = (status) => {
    const statusMap = {
      active: { label: 'Active', className: 'status-active' },
      draft: { label: 'Draft', className: 'status-draft' },
      inactive: { label: 'Inactive', className: 'status-inactive' },
    };

    const statusInfo = statusMap[status] || { label: status, className: '' };
    return (
      <span className={`status-badge ${statusInfo.className}`}>
        {statusInfo.label}
      </span>
    );
  };

  const columns = [
    {
      key: 'title',
      header: 'Topic Title',
      cellClassName: 'topic-title-cell',
    },
    {
      key: 'order',
      header: 'Order',
      cellClassName: 'topic-order-cell',
    },
    {
      key: 'status',
      header: 'Status',
      cellClassName: 'topic-status-cell',
      render: (status) => formatStatus(status)
    },
    {
      key: 'actions',
      header: 'Actions',
      cellClassName: 'topic-actions-cell',
      render: (_, topic) => (
        <div className="topic-actions">
          <button
            className="topic-edit-btn"
            onClick={(e) => {
              e.stopPropagation();
              handleEditTopic(topic);
            }}
            title="Edit topic"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
              <path d="m18.5 2.5 a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
          </button>
          <button
            className="topic-delete-btn"
            onClick={(e) => handleDeleteTopic(topic.id, e)}
            title="Delete topic"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M3 6h18"/>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
              <path d="M10 11v6"/>
              <path d="M14 11v6"/>
            </svg>
          </button>
        </div>
      )
    }
  ];

  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab="courses"
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />

      <div className="dashboard-main-content">
        <div className="topics-page-container">
          {/* Header Card */}
          <div className="topics-header-card">
            <h1 className="topics-page-title">
              {course ? course.title : 'Loading...'}
            </h1>
            <p className="course-description">
              {course ? course.description : 'Loading course details...'}
            </p>
          </div>

          {/* Controls Section */}
          <div className="topics-controls">
            <div className="topics-controls-row">
              <div className="topics-breadcrumb">
                <span onClick={() => navigate('/course')}>Courses</span>
                <span className="breadcrumb-separator">/</span>
                <span>Topics</span>
              </div>
              <button
                className="topics-add-btn"
                onClick={handleAddTopic}
              >
                Add New Topic
              </button>
            </div>
          </div>

          {/* Topics Table */}
          <div className="topics-datatable-wrapper">
            <DataTable
              data={topics}
              loading={loading}
              error={error}
              title=""
              columns={columns}
              onRowClick={(topic) => handleEditTopic(topic)}
              onRetry={() => window.location.reload()}
              emptyMessage="No topics available for this course."
              loadingMessage="Loading topics..."
              className=""
              tableClassName=""
              containerClassName=""
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Topics;
