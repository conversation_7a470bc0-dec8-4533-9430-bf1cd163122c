import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import AnimationBox from '../../components/common/AnimationBox';


import '../../styles/LoginPage.css';
import '../../styles/App.css';


const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');


  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');


  const navigate = useNavigate();
  const location = useLocation();
  const login = useAuthStore((state) => state.login);

  // Check for success message from UserDetails page
  useEffect(() => {
    if (location.state?.message) {
      setSuccessMessage(location.state.message);
      if (location.state?.email) {
        setEmail(location.state.email);
      }
      // Clear the message from location state to prevent it from persisting
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);



  const handleSubmit = async (e) => {
    e.preventDefault();

    let valid = true;
    setEmailError('');
    setPasswordError('');
    setSuccessMessage('');

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) {
      setEmailError('Email is required.');
      valid = false;
    } else if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address.');
      valid = false;
    }

    if (!password) {
      setPasswordError('Password is required.');
      valid = false;
    }

    if (!valid) return;

    setIsLoading(true);

    try {
      const result = await login(email, password);

      if (result.success) {
        setSuccessMessage('Login successful! Redirecting...');
        // Small delay to show success message
        setTimeout(() => {
          // Check for redirect parameter in URL
          const params = new URLSearchParams(location.search);
          const redirect = params.get('redirect');
          const destination = redirect || '/dashboard';
          console.log('[LoginPage] Redirecting to:', destination);
          navigate(destination, { replace: true });
        }, 1000);
      } else {
        // Display the error message from the API
        setPasswordError(result.error || 'Login failed. Please check your credentials.');
      }
    } catch (error) {
      console.error('Login error:', error);
      // Display exact error message from backend
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'Login failed. Please check your credentials and try again.';
      setPasswordError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };





  return (
    <div className="login-container">


      <AnimationBox className="login-box">

        <h4 className='h4'>Sign In</h4>

        {/* Success message from UserDetails completion */}
        {successMessage && (
          <div className="success-message" style={{
            backgroundColor: '#d4edda',
            color: '#155724',
            padding: '10px',
            borderRadius: '4px',
            marginBottom: '15px',
            border: '1px solid #c3e6cb'
          }}>
            {successMessage}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="text"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."

            />

            {emailError && <div className="error-message">{emailError}</div>}
          </div>
          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <div className="password-wrapper">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password..."

              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowPassword((prev) => !prev)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
              </button>
            </div>
            {passwordError && <div className="error-message">{passwordError}</div>}
          </div>

          {successMessage && <div className="success-message">{successMessage}</div>}

          <button
            type="submit"
            className="body3-bold login-button"
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign in'}
          </button>
        </form>

      </AnimationBox>
    </div>
  );
};

export default LoginPage;