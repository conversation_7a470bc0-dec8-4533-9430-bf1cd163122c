import React from 'react';
import Card from '../ui/Card';
import '../../styles/DashboardMainContent.css';

const DashboardMainContent = ({
  activeTab,
  courses,
  loading,
  error,
  onCourseClick
}) => {
  const renderHomeContent = () => {
    // Loading state
    if (loading) {
      return (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p className="body2 text-secondary">Loading courses...</p>
        </div>
      );
    }

    // Error state
    if (error) {
      return (
        <div className="error-container">
          <p className="body2 text-error">Failed to load courses: {error}</p>
          <button
            className="retry-button"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      );
    }

    // Empty state
    if (!courses || courses.length === 0) {
      return (
        <div className="empty-container">
          <p className="body2 text-secondary">No courses available at the moment.</p>
        </div>
      );
    }

    // Courses grid
    return (
      <div className="courses-grid">
        {courses.map((course) => (
          <Card
            key={course.id}
            title={course.title}
            price={course.price}
            duration={course.duration}
            image={course.image}
            alt={`${course.title} course thumbnail`}
            onCardClick={() => onCourseClick(course.id)}
            className="course-card"
          />
        ))}
      </div>
    );
  };



  const renderCategoriesContent = () => (
    <div className="categories-section">
      <h2 className="h2 text-primary">Categories</h2>
      <p className="body2 text-secondary">Categories content coming soon...</p>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return renderHomeContent();
      case 'categories':
        return renderCategoriesContent();
      default:
        return renderHomeContent();
    }
  };

  return (
    <div className="dashboard-main-content">
      <div className="content-container">
        {renderContent()}
      </div>
    </div>
  );
};

export default DashboardMainContent;
