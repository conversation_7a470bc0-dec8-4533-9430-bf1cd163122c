import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import AddCourse from '../AddCourse';
import { useAuthStore } from '../../stores/authStore';
import { coursesAPI } from '../../services/api';

// Mock the auth store
vi.mock('../../stores/authStore', () => ({
  useAuthStore: vi.fn(),
}));

// Mock the API
vi.mock('../../services/api', () => ({
  coursesAPI: {
    create: vi.fn(),
  },
}));

// Mock navigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('AddCourse Component', () => {
  const mockAuthStore = {
    isAuthenticated: true,
    currentUser: { id: 'test-user', email: '<EMAIL>' },
    logout: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    useAuthStore.mockReturnValue(mockAuthStore);
  });

  it('should render the add course form', () => {
    renderWithRouter(<AddCourse />);

    expect(screen.getByText('Add New Course')).toBeInTheDocument();
    expect(screen.getByLabelText(/Course Title/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Description/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Original Price/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Offer Price/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Tags/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Status/)).toBeInTheDocument();
    expect(screen.getByText('Create Course')).toBeInTheDocument();
  });

  it('should show validation errors for required fields', async () => {
    renderWithRouter(<AddCourse />);

    const submitButton = screen.getByText('Create Course');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Title is required')).toBeInTheDocument();
      expect(screen.getByText('Description is required')).toBeInTheDocument();
      expect(screen.getByText('Original price is required')).toBeInTheDocument();
    });
  });

  it('should validate price fields correctly', async () => {
    renderWithRouter(<AddCourse />);

    const originalPriceInput = screen.getByLabelText(/Original Price/);
    const offerPriceInput = screen.getByLabelText(/Offer Price/);

    // Test invalid original price
    fireEvent.change(originalPriceInput, { target: { value: '-10' } });
    fireEvent.change(offerPriceInput, { target: { value: '5' } });

    const submitButton = screen.getByText('Create Course');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid price')).toBeInTheDocument();
    });

    // Test offer price higher than original price
    fireEvent.change(originalPriceInput, { target: { value: '100' } });
    fireEvent.change(offerPriceInput, { target: { value: '150' } });

    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Offer price must be less than original price')).toBeInTheDocument();
    });
  });

  it('should handle file upload', () => {
    renderWithRouter(<AddCourse />);

    const fileInput = screen.getByLabelText(/Display Picture/);
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    fireEvent.change(fileInput, { target: { files: [file] } });

    expect(screen.getByText('test.jpg')).toBeInTheDocument();
  });

  it('should validate file type', async () => {
    renderWithRouter(<AddCourse />);

    const fileInput = screen.getByLabelText(/Display Picture/);
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });

    fireEvent.change(fileInput, { target: { files: [invalidFile] } });

    await waitFor(() => {
      expect(screen.getByText('Please select a valid image file (JPG, PNG, or WebP)')).toBeInTheDocument();
    });
  });

  it('should submit form with valid data', async () => {
    coursesAPI.create.mockResolvedValue({ success: true });
    renderWithRouter(<AddCourse />);

    // Fill form with valid data
    fireEvent.change(screen.getByLabelText(/Course Title/), {
      target: { value: 'Test Course' }
    });
    fireEvent.change(screen.getByLabelText(/Description/), {
      target: { value: 'Test Description' }
    });
    fireEvent.change(screen.getByLabelText(/Original Price/), {
      target: { value: '100' }
    });
    fireEvent.change(screen.getByLabelText(/Offer Price/), {
      target: { value: '80' }
    });
    fireEvent.change(screen.getByLabelText(/Tags/), {
      target: { value: 'programming, web' }
    });

    const submitButton = screen.getByText('Create Course');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(coursesAPI.create).toHaveBeenCalledWith({
        title: 'Test Course',
        description: 'Test Description',
        originalPrice: 100,
        offerPrice: 80,
        tags: ['programming', 'web'],
        status: 'Active'
      });
    });

    expect(mockNavigate).toHaveBeenCalledWith('/course');
  });

  it('should handle API errors', async () => {
    coursesAPI.create.mockRejectedValue(new Error('API Error'));
    renderWithRouter(<AddCourse />);

    // Fill required fields
    fireEvent.change(screen.getByLabelText(/Course Title/), {
      target: { value: 'Test Course' }
    });
    fireEvent.change(screen.getByLabelText(/Description/), {
      target: { value: 'Test Description' }
    });
    fireEvent.change(screen.getByLabelText(/Original Price/), {
      target: { value: '100' }
    });

    const submitButton = screen.getByText('Create Course');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('API Error')).toBeInTheDocument();
    });
  });

  it('should navigate back to courses page when back button is clicked', () => {
    renderWithRouter(<AddCourse />);

    const backButton = screen.getByText('← Back to Courses');
    fireEvent.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith('/course');
  });

  it('should navigate back to courses page when cancel button is clicked', () => {
    renderWithRouter(<AddCourse />);

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockNavigate).toHaveBeenCalledWith('/course');
  });
});
