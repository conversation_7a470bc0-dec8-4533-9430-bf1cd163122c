import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import DataTable from '../DataTable';

describe('DataTable Component', () => {
  const mockData = [
    {
      id: 1,
      name: 'Test Item 1',
      status: 'active',
      price: 100
    },
    {
      id: 2,
      name: 'Test Item 2',
      status: 'inactive',
      price: 200
    }
  ];

  const mockColumns = [
    {
      key: 'name',
      header: 'Name',
      cellClassName: 'name-cell'
    },
    {
      key: 'status',
      header: 'Status',
      render: (value) => (
        <span className={`status-badge status-${value}`}>
          {value.toUpperCase()}
        </span>
      )
    },
    {
      key: 'price',
      header: 'Price',
      formatter: (value) => `$${value}`
    }
  ];

  const defaultProps = {
    data: mockData,
    columns: mockColumns,
    title: 'Test Data',
    loading: false,
    error: null
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render table with data correctly', () => {
    render(<DataTable {...defaultProps} />);

    expect(screen.getByText('Test Data')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Price')).toBeInTheDocument();
    
    expect(screen.getByText('Test Item 1')).toBeInTheDocument();
    expect(screen.getByText('Test Item 2')).toBeInTheDocument();
    expect(screen.getByText('ACTIVE')).toBeInTheDocument();
    expect(screen.getByText('INACTIVE')).toBeInTheDocument();
    expect(screen.getByText('$100')).toBeInTheDocument();
    expect(screen.getByText('$200')).toBeInTheDocument();
  });

  it('should render loading state', () => {
    render(<DataTable {...defaultProps} loading={true} />);

    expect(screen.getByText('Loading data...')).toBeInTheDocument();
    expect(screen.getByText('Test Data')).toBeInTheDocument();
  });

  it('should render error state', () => {
    render(<DataTable {...defaultProps} error="Failed to load data" />);

    expect(screen.getByText('Failed to load test data: Failed to load data')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('should render empty state', () => {
    render(<DataTable {...defaultProps} data={[]} />);

    expect(screen.getByText('No data available at the moment.')).toBeInTheDocument();
  });

  it('should call onRowClick when row is clicked', () => {
    const mockOnRowClick = vi.fn();
    render(<DataTable {...defaultProps} onRowClick={mockOnRowClick} />);

    const firstRow = screen.getByText('Test Item 1').closest('tr');
    fireEvent.click(firstRow);

    expect(mockOnRowClick).toHaveBeenCalledWith(mockData[0]);
  });

  it('should call onRetry when retry button is clicked', () => {
    const mockOnRetry = vi.fn();
    render(<DataTable {...defaultProps} error="Test error" onRetry={mockOnRetry} />);

    const retryButton = screen.getByText('Retry');
    fireEvent.click(retryButton);

    expect(mockOnRetry).toHaveBeenCalled();
  });

  it('should apply custom CSS classes', () => {
    render(
      <DataTable 
        {...defaultProps} 
        className="custom-table"
        tableClassName="custom-table-element"
        containerClassName="custom-container"
      />
    );

    expect(document.querySelector('.custom-table')).toBeInTheDocument();
    expect(document.querySelector('.custom-table-element')).toBeInTheDocument();
    expect(document.querySelector('.custom-container')).toBeInTheDocument();
  });

  it('should render custom messages', () => {
    render(
      <DataTable 
        {...defaultProps} 
        data={[]}
        emptyMessage="Custom empty message"
        loadingMessage="Custom loading message"
        loading={true}
      />
    );

    expect(screen.getByText('Custom loading message')).toBeInTheDocument();
  });
});
