/**
 * DataTable Component
 * 
 * @param {Object} props - Component props
 * @param {Array<Object>} [props.data=[]] - Array of data objects to display
 * @param {boolean} [props.loading=false] - Whether the data is currently loading
 * @param {Error} [props.error=null] - Error object if data loading failed
 * @param {string} [props.title=''] - Table title
 * @param {Array<Object>} [props.columns=[]] - Column configuration
 * @param {Function} [props.onRowClick] - Callback when a row is clicked
 * @param {Function} [props.onRetry] - Callback when retry button is clicked
 * @param {string} [props.emptyMessage='No data available at the moment.'] - Message to show when there's no data
 * @param {string} [props.loadingMessage='Loading data...'] - Message to show while loading
 * @param {string} [props.className=''] - Additional class name for the table container
 * @param {string} [props.tableClassName=''] - Additional class name for the table element
 * @param {string} [props.containerClassName=''] - Additional class name for the table wrapper
 * @param {boolean} [props.showHeader=true] - Whether to show the table header
 * @param {string} [props.rowKey='id'] - Key to use for React list keys
 * @param {boolean} [props.stickyHeader=true] - Whether to make the header sticky
 */
import React, { useMemo } from 'react';
import '../../styles/DataTable.css';

const DataTable = ({
  data = [],
  loading = false,
  error = null,
  title = 'Data',
  columns = [],
  onRowClick,
  onRetry,
  emptyMessage = 'No data available at the moment.',
  loadingMessage = 'Loading data...',
  className = '',
  tableClassName = '',
  containerClassName = '',
  showHeader = true,
  rowKey = 'id',
  stickyHeader = true
}) => {
  // Helper function to render cell content based on column configuration
  const renderCellContent = (item, column) => {
    const value = item[column.key];

    if (column.render) {
      return column.render(value, item);
    }

    if (column.formatter) {
      return column.formatter(value);
    }

    return value || 'N/A';
  };

  // Memoize the table header to prevent unnecessary re-renders
  const tableHeader = useMemo(() => {
    if (!showHeader) return null;
    
    return (
      <thead>
        <tr>
          {columns.map((column, index) => (
            <th 
              key={column.key || index}
              className={column.headerClassName || ''}
              style={{
                width: column.width || 'auto',
                minWidth: column.minWidth || '100px',
                ...(column.headerStyle || {})
              }}
            >
              {column.header}
            </th>
          ))}
        </tr>
      </thead>
    );
  }, [columns, showHeader]);

  // Loading state
  if (loading) {
    return (
      <div className={`data-table-page ${className}`}>
        {title && <h2>{title}</h2>}
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p className="loading-message">{loadingMessage}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`data-table-page ${className}`}>
        {title && <h2>{title}</h2>}
        <div className="error-container">
          <p className="error-message">Failed to load {title.toLowerCase()}: {error.message || 'An error occurred'}</p>
          {onRetry && (
            <button
              className="retry-button"
              onClick={onRetry}
            >
              Retry
            </button>
          )}
        </div>
      </div>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <div className={`data-table-page ${className}`}>
        {title && <h2>{title}</h2>}
        <div className="empty-container">
          <p className="empty-message">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`data-table-page ${className}`}>
      {title && <h2>{title}</h2>}
      <div className="data-table-content">
        <div className={`data-table-container ${containerClassName}`}>
          <div className="data-table-wrapper">
            <table 
              className={`data-table ${tableClassName}`}
              style={{
                '--sticky-header': stickyHeader ? 'sticky' : 'static'
              }}
            >
              {tableHeader}
              <tbody>
                {data.map((item, rowIndex) => {
                  const rowKeyValue = item[rowKey] || rowIndex;
                  return (
                    <tr
                      key={rowKeyValue}
                      className="data-row"
                      onClick={() => onRowClick && onRowClick(item, rowIndex)}
                      style={{ 
                        cursor: onRowClick ? 'pointer' : 'default',
                        ...(item.rowStyle || {})
                      }}
                    >
                      {columns.map((column, colIndex) => {
                        const cellKey = `${rowKeyValue}-${column.key || colIndex}`;
                        const cellContent = renderCellContent(item, column);
                        
                        return (
                          <td 
                            key={cellKey}
                            className={column.className || ''}
                            style={column.style || {}}
                            data-label={column.header}
                          >
                            {cellContent}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

/** @type {Object} Default props for the DataTable component */
DataTable.defaultProps = {
  data: [],
  loading: false,
  error: null,
  title: '',
  columns: [],
  onRowClick: null,
  onRetry: null,
  emptyMessage: 'No data available at the moment.',
  loadingMessage: 'Loading data...',
  className: '',
  tableClassName: '',
  containerClassName: '',
  showHeader: true,
  rowKey: 'id',
  stickyHeader: true
};

export default DataTable;
